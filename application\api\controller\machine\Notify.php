<?php

namespace app\api\controller\machine;

use app\common\model\Ad;
use app\common\model\vending\Config;
use app\common\model\vending\goods\Category;
use app\common\model\vending\goods\Goods;
use app\common\model\vending\machine\MachineStatus;
use app\common\model\vending\machine\Type;
use app\common\model\vending\machine\Way;
use app\common\service\machine\WayService;
use think\exception\ValidateException;

/**
 * 售货机首页接口
 */
class Notify extends Base
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];


    public function index(){

    }

}
