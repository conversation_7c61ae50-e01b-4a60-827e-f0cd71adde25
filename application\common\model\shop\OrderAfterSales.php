<?php

namespace app\common\model\shop;

use think\Model;

/**
 * 订单售后申请模型
 */
class OrderAfterSales extends Model
{
    protected $name = 'order_after_sales';
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'int';
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 售后类型
    const TYPE_REFUND_ONLY = 1; // 仅退款
    const TYPE_RETURN_REFUND = 2; // 退货退款
    
    // 售后状态
    const STATUS_PENDING = 0; // 待审核
    const STATUS_APPROVED = 1; // 审核通过
    const STATUS_REJECTED = 2; // 审核驳回
    const STATUS_REFUNDED = 3; // 已退款
    const STATUS_CLOSED = 4; // 已关闭
    
    /**
     * 生成售后申请单号
     */
    public static function generateApplicationSn()
    {
        $prefix = 'AS';
        $timestamp = date('YmdHis');
        $random = mt_rand(1000, 9999);
        return $prefix . $timestamp . $random;
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '审核通过',
            self::STATUS_REJECTED => '审核驳回',
            self::STATUS_REFUNDED => '已退款',
            self::STATUS_CLOSED => '已关闭'
        ];
        return $statusMap[$data['status']] ?? '未知状态';
    }
    
    /**
     * 获取类型文本
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = [
            self::TYPE_REFUND_ONLY => '仅退款',
            self::TYPE_RETURN_REFUND => '退货退款'
        ];
        return $typeMap[$data['type']] ?? '未知类型';
    }
    
    /**
     * 关联售货机订单
     */
    public function vendingOrder()
    {
        return $this->belongsTo('app\common\model\vending\machine\Order', 'order_id');
    }

    /**
     * 关联商城订单（保留兼容性）
     */
    public function order()
    {
        return $this->belongsTo('Order', 'order_id');
    }

    /**
     * 验证是否可以退款
     * @return bool
     */
    public function canRefund()
    {
        return $this->status == self::STATUS_APPROVED;
    }

    /**
     * 验证是否已经退款
     * @return bool
     */
    public function isRefunded()
    {
        return $this->status == self::STATUS_REFUNDED;
    }

    /**
     * 获取退款金额
     * @return float
     */
    public function getRefundAmount()
    {
        return $this->approved_amount ?: $this->requested_amount;
    }

    /**
     * 获取关联订单的剩余可退款金额
     * @return float
     */
    public function getOrderRemainRefundAmount()
    {
        $order = $this->vendingOrder;
        if (!$order) {
            return 0;
        }

        return $order->pay_fee - $order->refunded_amount;
    }

    /**
     * 验证退款金额是否有效
     * @param float $amount
     * @return bool
     */
    public function validateRefundAmount($amount)
    {
        if ($amount <= 0) {
            return false;
        }

        $remainingAmount = $this->getOrderRemainRefundAmount();
        return $amount <= $remainingAmount;
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('app\common\model\User', 'user_id');
    }
}
