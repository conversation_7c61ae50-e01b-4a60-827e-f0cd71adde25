<?php

namespace app\api\controller\machine;

use app\admin\model\vending\machine\order\Goods;
use app\common\controller\Api;
use app\common\model\Coupons;
use app\common\model\ScoreLog;
use app\common\model\UserCoupons;
use app\common\model\UserExchangeCoupons;
use app\common\model\vending\machine\OrderGoods;
use app\common\service\CouponService;
use app\common\service\machine\PayService;
use app\common\service\UserService;
use app\gateway\workerman\handler\PushHandler;
use GatewayWorker\Lib\Gateway;
use Symfony\Component\HttpFoundation\Request;
use think\Log;
use Yansongda\Pay\Gateways\Wechat\Support;

/**
 * 售货机支付接口
 */
class Pay extends Api
{
    protected $noNeedLogin = ['notify'];

    //收银台
    public function index()
    {
        [$order_sn] = $this->params([
            ['order_sn', '']
        ]);
        if ($order_sn == '') $this->error('订单编号为空');
        $order = \app\common\model\vending\machine\Order::where('order_sn', $order_sn)->field('id,total_amount,order_sn,pay_amount,status,discount_amount,createtime')->find();
        if (!$order || $order['status'] > 0) $this->error('订单信息有误');
        //支付剩余时间计算
        $order_config = get_config('order');
        $order_close_time = $order_config['order_auto_close'];
        $order_close_time = $order_close_time * 60;
        $user_money = $this->auth->money;
        $user_score = $this->auth->score;
        $coupons = [];
        $couponService = new CouponService();
        $user_coupons = $couponService->getUserCoupons($this->auth->id, CouponService::COUPONS_CAN_USE); //获取用户可用的优惠券
        $goods_list = Goods::where(['order_id'=>$order->id])->select();
        $max=0;
        $tag = 0;
        foreach ($user_coupons as $coupon) {
            if ($coupon['goods_ids'] === '0') {
                //所有商品可用
                $can_use = true;
            } else {
                //指定商品可用，如果有一个产品适用，则优惠券允许使用
                $goods_ids = explode(',', $coupon['goods_ids']);
                $can_use = false;
                foreach ($goods_list as $goods) {
                    //使用兑换券，则优惠券就不可用了
                    if (!in_array($goods['goods_id'], $goods_ids) && empty($goods['exchange_coupon_id'])) {
                        continue; //继续比较下一个商品
                    }
                    $can_use = true; //当订单张有一个商品可以使用就可用
                    break;
                }
            }

            // 商品可用 并且 订单金额满足门槛
            if ($can_use && $coupon->enough <= $order['total_amount']) {

                switch ($coupon->type) {
                    case 1:
                        $tag =$coupon->amount ;
                        break;
                    case 2:
                        $tag =  $coupon->amount ;
                        break;
                    case 3:
                        $tag = round($coupon->discount * $order['total_amount'],2);
                        break;
                    default:

                }
                if($tag>$max){
                    if($tag>$order['total_amount']){
                        $tag=$order['total_amount'];
                    }
                    $coupon['tag'] = $tag;
                    $coupons = $coupon;

                }

            }
        }
        if($tag>0){

            \app\common\model\vending\machine\Order::where('order_sn', $order_sn)->update([
                'coupon_amount'=>$tag,
                'user_coupon_id'=>$coupon['user_coupons_id'],
                'pay_amount'=>$order['total_amount']-$tag
            ]);
            UserCoupons::where(['id' => $coupon['user_coupons_id']])->update([
                'usetime'=>time()
            ]);

            $order = \app\common\model\vending\machine\Order::where('order_sn', $order_sn)->find();
        }
//        $this->order['coupons'] = $coupons;

        if(empty($coupons)){
            $coupons=null;
        }
        $this->success('', compact('order', 'user_money', 'user_score', 'order_close_time','coupons'));
    }

    /**
     * 拉起支付
     */
    public function create()
    {
        [$order_sn, $payment] = $this->params([
            ['order_sn', ''],
            ['payment', ''], //支付方式 wechat alipay wallet xinyi card
        ]);
        if (empty($order_sn)) $this->error('订单编号为空');
        if (!$payment || !in_array($payment, ['wechat', 'alipay', 'wallet','xinyi','card'])) {
            $this->error("支付类型有误");
        }
//        if ($payment == 'wallet' && $this->machine->agent_id > 0) {
//            $this->error('此设备暂不支持余额支付');
//        }
        $payService = new PayService($this->platform, $payment);
        $this->success('获取预付款成功', $payService->create($order_sn));
    }

    /**
     * 支付成功回调
     * @return mixed|void
     */
    public function notify()
    {
        $content = $content ?? Request::createFromGlobals()->getContent();
        $res_data = Support::fromXml($content);
        trace('支付回调参数');
        trce($res_data);
        $out_trade_no = $res_data['out_trade_no'];


        $order = (new \app\admin\model\vending\machine\Order())->where('order_sn', $out_trade_no)->find();
        Log::write(1);
        if (!$order || $order->status > 0) {
            // 订单不存在，或者订单已支付
            $this->error("订单不存在，或者订单已出货");
        }
        Log::write(2);
        $order->status = 1;
        $order->paytime = time();
        $order->save();
        Log::write(3);
        //充值订单，
//        $items= OrderGoods::where('order_id', $order['id'])->select();



        OrderGoods::where('order_id', $order['id'])->update(['paytime' => $order['paytime'], 'pay_type' => $order['pay_type'], 'pay_status' => 1]);
        Log::write(4);
//        $ways = [];
//
//        foreach ($items as $item) {
//            for ($i =0;$i<$item['goods_num'];$i++){
//                $ways[] = $item['way'];
//            }
//        }
//
//        $pushHandler = new PushHandler();
//        $pushHandler->multiGoodsOut( $order->device_no, $order->order_sn,$ways);
        $user = \app\admin\model\User::get($order->user_id);
        $scoreConfig = get_config('score', $order['agent_id']);
        $score = bcmul($order['pay_amount'], $scoreConfig['score_rate'], 0);
        if ($score > 0) {
            \app\common\service\UserService::changeScore($user, $score, 1, '订单赠送积分', $order['order_sn']);
        }
        $coupon = Coupons::where('type', 4)->select();
        Log::write(5);
        foreach ($coupon as $k=>$v){

                 $row=UserExchangeCoupons::create([
                    'user_id' => $this->auth->id,
                    'coupon_id' => $v['id'],
                    'name' => $v['name'],
                    'goods_id' => $v['goods_ids'],
                    'createtime' => time()
                ]);
                 $this->exchange($row->id);

        }
        Log::write(6);
        if($order->platform=='android'){
            $config = config('workerman');
            Gateway::$registerAddress = '127.0.0.1:'.$config['register_port'];
            $send_data=[
                'DEVICE'=>$order->device_no,
                'CMD'=>3,
                'SEQ'=>time()
            ];
            Gateway::sendToUid($order->device_no,json_encode($send_data));
            Log::write($config['register_port']);
            sleep(2);
            $item = OrderGoods::where('order_id', $order['id'])->find();
            $goods = \app\admin\model\vending\Goods::get($item->goods_id);
            $send_data=[
                'DEVICE'=>$order->device_no,
                'CMD'=>2,
                'WEIGHT'=>round($item['goods_num'] * $goods['spec_num']),
                'TYPE'=>$goods['category_id'],
                'SEQ'=>$order->order_sn
            ];
            Log::write('发送通知'.json_encode($send_data));
            Gateway::sendToUid($order->device_no,json_encode($send_data));

            (new \app\admin\model\vending\machine\Order())->where('order_sn', $out_trade_no)->update([
                'status'=>'3'
            ]);


        }
//        \think\Queue::push('\app\common\job\machine\Order@payed', ['order_id' => $order['id'], 'user_id' => $order['user_id']]);

        $this->success('操作成功，请等待出货！');
    }
    protected function exchange($code)
    {


        $params['text'] = $code;
        $mimetype = 'png'  ;
        $qrCode = \addons\qrcode\library\Service::qrcode($params);
        // 写入到文件
        if (1) {

            $qrcodePath = ROOT_PATH .  'public/qrcode/exchange/';
            if (!is_dir($qrcodePath)) {
                @mkdir($qrcodePath);
            }
            if (is_really_writable($qrcodePath)) {
                $filePath = $qrcodePath . $code . '.png' ;

                $qrCode->writeFile($filePath);
            }
        }
    }

}
