<?php

namespace app\api\controller;

use app\admin\model\Advertisement;
use app\api\library\Pay;
use app\common\controller\Api;
use app\common\library\RedisClient;
use app\common\model\Ad;
use app\common\model\Config;
use app\common\model\ProfitLog;
use app\common\model\UserOauth;
use app\common\model\vending\goods\Goods;
use app\common\model\vending\goods\GoodsSales;
use app\common\model\vending\machine\Group;
use app\common\model\vending\machine\Machine;
use app\common\model\vending\machine\MachineStatus;
use app\common\model\vending\machine\Order;
use app\common\model\vending\machine\OrderGoods;
use app\common\model\vending\machine\OutLog;
use app\common\model\vending\machine\Type;
use app\common\model\vending\machine\Way;
use app\common\service\UserExchangeCouponsService;
use app\common\service\UserService;
use app\gateway\workerman\handler\PushHandler;
use app\gateway\workerman\handler\ReportHandler;
use app\gateway\workerman\Log;
use app\gateway\workerman\Message;
use GatewayWorker\Gateway;
use think\Db;
use think\Request;

/**
 * 首页接口，此文件未用到
 */
class Index extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function switch()
    {
//        $order = Order::get(291);
//
//        $item = OrderGoods::where([
//            'order_id'=>291
//        ])->find();
//        $refund_amount = $item['pay_amount'];
//
//        \app\admin\model\vending\machine\Order::startRefund($order, $item, $refund_amount, null, '出货失败退款');
//        die;

        $this->success('ok',1);
    }

    /**
     * 发送消息
     * @param $client_id
     * @param $message
     */
    private static function sendMsg($client_id,$message)
    {
        Gateway::sendToClient($client_id,$message);
    }

    /**
     * 获取配送电话
     */
    public function pstel()
    {
        $kefu_tel = Config::where('id',27)->value('value');
        $this->success('ok',$kefu_tel);
    }
    /**
     * 获取客服电话
     */
    public function tel()
    {
        $kefuConfig = get_config('chat');
//        $kefu_tel = Config::where('id',24)->value('value');
        $this->success('ok',$kefuConfig['kefu_mobile']);
    }

    /**
     * 附近设备
     */
    public function near()
    {

        $machine = (new Machine())->where(['id'=>3])->find();
        $group_user_id = (new Group())->where([
            'id'=>$machine['group_id']
        ])->value('user_id');

        $user = \app\admin\model\User::get($group_user_id);


    
        $params  = input('post.');
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 15;
        $lng = $params['lng'] ?? 0;
        $lat = $params['lat'] ?? 0;
        $start = max($page - 1,0);
        if($lng>0&&$lat>0){

            $total = (new Machine())->count();
            $list= (new Machine())->field("*,ROUND(6378.138*2*ASIN(SQRT(POW(SIN(({$lat}*PI()/180-lat*PI()/180)/2),2)+COS({$lat}*PI()/180)*COS(lat*PI()/180)*POW(SIN(({$lng}*PI()/180-lon*PI()/180)/2),2)))*1000) as distance")
                ->order('distance')->limit($start, $limit)->select();

            foreach ($list as $k=>$v){
                $list[$k]['distance'] = round( $v['distance'] / 1000,2).'km';
                $category = Type::where([
                    'id'=>$v['type_id']
                ])->value('category');
                $category_str = [];
                if(strpos($category,'1')!==false){
                    $category_str[]= '块冰';
                }
                if(strpos($category,'2')!==false){
                    $category_str[]= '碎冰';
                }
                $list[$k]['category'] = $category_str;
            }

        }else{
            $this->error('定位失败，请检查定位设置并重试！');
        }
        $this->success('', compact('total','list'));
    }
    protected function exchange($code)
    {


        $params['text'] = $code;
        $mimetype = 'png'  ;
        $qrCode = \addons\qrcode\library\Service::qrcode($params);
        // 写入到文件
        if (1) {

            $qrcodePath = ROOT_PATH .  'public/qrcode/exchange/';
            if (!is_dir($qrcodePath)) {
                @mkdir($qrcodePath);
            }
            if (is_really_writable($qrcodePath)) {
                $filePath = $qrcodePath . $code . '.png' ;

                $qrCode->writeFile($filePath);
            }
        }
    }


    public function job_send()
    {
        echo '2123';
        die;
// 使用示例

//        $this->exchange(1);
//        die;
//        dump($_SERVER);
//        $randomBytes = openssl_random_pseudo_bytes(8);
//// 将随机字节串转换为十六进制字符串
//        $randomString = bin2hex($randomBytes);
//
//        echo $randomString;


//        $data ='{
//            "DEVICE":"1234567890",
//            "CMD":2,
//            "WEIGHT":1,
//            "TYPE":1,
//            "SEQ":"000XXXX"
//        }';
        // 加密密钥，长度必须是 16、24 或 32 字节
        $key = '08f46334bfa3f71783890006655e4dc6';
        // 初始化向量，AES-128 模式下长度为 16 字节

        $iv = '4a1586659a32ba62';

        // 加密数据
        $encrypted = 'vdwXdMSp0extX0EfkCk3umGcujWorp0kCpXc49PY9VBCKQEqivOyjJH76PCPj+6uagvY7uU8+GMdzbH8JKhyFxPKwvbJOC3NpKWG98Nn94M=';
        $encrypted = openssl_decrypt(base64_decode($encrypted), 'AES-256-CBC', $key, OPENSSL_RAW_DATA, $iv);
        dump($encrypted);
        die;
        echo $encrypted;
        die;
        $p =  new PushHandler();
        $p->stockstartStop('868330076764945','0001');
        return ;
        [$deviceNo,$way] = $this->params(['deviceNo', 'way']);

        try {
            $pushHandler = new PushHandler();
            $pushHandler->remoteTestNo($deviceNo, $way);
            $this->success('ok');
        } catch (\Exception $e) {

            $this->error($e->getMessage());
        }

//        Gateway::sendToClient($deviceNo, $message);
//        $msg = unpack($message);
//        $this->log->setMesage($msg)->reply();
//        \think\Log::write('指令下发成功','log',true);
    }
    public function job_open()
    {
        dump($_SERVER);
        die;
        [$deviceNo] = $this->params([['deviceNo']]);

        try {
            $pushHandler = new PushHandler();
            $pushHandler->RemoteDoor($deviceNo);
            $this->success('ok');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
//        Gateway::sendToClient($deviceNo, $message);
//        $msg = unpack($message);
//        $this->log->setMesage($msg)->reply();
//        \think\Log::write('指令下发成功','log',true);
    }
    //远程关闭库存管理
    public function job_stock_close()
    {
        [$deviceNo] = $this->params([['deviceNo']]);

        try {
            $pushHandler = new PushHandler();
            $pushHandler->stockstartStop($deviceNo,1);
            $this->success('ok');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
//        Gateway::sendToClient($deviceNo, $message);
//        $msg = unpack($message);
//        $this->log->setMesage($msg)->reply();
//        \think\Log::write('指令下发成功','log',true);
    }
    /**
     * 首页
     *
     */
    public function index()
    {


       
        $callback  = new Stompcallback();
        $res = $callback->analysis('{SR,1725393521,TP11024090001,868294069893519,898604D9262390217809,31,TP110_A76_V1.240904.1,2,0,40,50,1:1;2:0;,BA77}');
        dump($res);
        die;
        halt($this->request->param());
        $this->success('请求成功');
    }
    public function test_out(){
        $config = config('workerman');
        \GatewayWorker\Lib\Gateway::$registerAddress = '127.0.0.1:'.$config['register_port'];
//        $send_data=[
//            'DEVICE'=>20250120,
//            'CMD'=>2,
//            'WEIGHT'=>10,
//            'TYPE'=>1,
//            'SEQ'=>20250703633995
//        ];
        $res=\GatewayWorker\Lib\Gateway::getAllClientIdList();
        dump($res);
        die;
        echo '发送数据'.json_encode($res);
        die;
        $list =\GatewayWorker\Lib\Gateway::sendToUid();
        dump($list);
        die;
        $fault_arr = [
            '1'=>'碎冰机故障',
            '2'=>'绞龙电机故障',
            '3'=>'拉冰电机故障',
            '4'=>'侧推电机故障',
            '5'=>'落冰超时故障',
            '6'=>'落冰限位开关故障',
            '7'=>'推冰限位开关故障',
            '8'=>'制冰机故障',

        ];

        $oauth = UserOauth::where([
            'user_id'=>21
        ])->find();


        $data = array(
            "touser" =>  $oauth['mp_openid'],
            "template_id" => 'dO7C6DklX2i4cCdaHOlxVnSUcPSwdWluAZ8UvSt3jSc',
            "url" => '',
            "data" => array(
                'thing1' => array('value' =>'123212', 'color' => '#173177'),
                'thing2' => array('value' => '1231212', 'color' => '#173177'),
                'time3' => array('value' => date('Y-m-d H:i:s'), 'color' => '#173177'),
                'const4' => array('value' => '制冰机故障', 'color' => '#173177'),

            ),
        );
        dump($data);
        $res = (new Push())->send($data);
dump($res);
die;

        $res = \app\common\library\Sms::notice($user['mobile'],'您的售卖冰块小程序当前出现离线或故障，请及时查看设备状态并处理，以确保
服务正常运行。如有疑问可咨询客服','SMS_489700295');

        $message = '{"DEVICE":"20250120","CMD":1,"WEIGHT":30,"TEMP":-10,"STATE":1,"SEQ":"000XXXX"}';

        $data = json_decode($message,true);

        $cmd = $data['CMD'];
        $device_no = $data['DEVICE'];
        $seq = $data['SEQ'];
        switch ($cmd){
            case '1'://心跳:
                \app\admin\model\vending\machine\Log::create([
                    'device_no'=>$device_no,
                    'hex_data'=>$message,
                    'format_data'=>$message,
                    'frame_type'=>1,
                    'type'=>'1',
                    'createtime'=>time()
                ]);
                $device_info = \app\admin\model\vending\Machine::where(['device_no'=>$device_no])->find();

                $user_id = Group::where([
                    'id'=>$device_info['group_id']
                ])->value('user_id');

                $user = \app\admin\model\User::get($user_id);

                $machine_status =  MachineStatus::where([
                    'device_no'=>$device_no
                ])->find();
                if($machine_status){
                    MachineStatus::where([
                        'device_no'=>$device_no
                    ])->update([
                        'status'=>'1'
                    ]);
                }else{
                    MachineStatus::create([
                        'machine_id'=>$device_info['id'],
                        'device_no'=>$device_no,
                        'status'=>'1',
                    ]);
                }

                $state = $data['STATE'] ?? 0;

                if($state!=0){
                    (new Index())->send_gz_msg($device_info,$user,$state);
                }

                $responseMessage='{"DEVICE":"'.$device_no.'","CMD":1,"SEQ":"'.$seq.'"}';
dump($responseMessage);
die;

                break;
        }
        $message='{"DEVICE":"20250120","CMD":2,"PORT":1,"WEIGHT":1,"TYPE":1,"SEQ":"20250612594847"}';
        $data = json_decode($message,true);
        $cmd = $data['CMD'];
        $device_no = $data['DEVICE'];
        $seq = $data['SEQ'];
        switch ($cmd){
            case '2':
                $seq = $data['SEQ'];
                $order = \app\admin\model\vending\machine\Order::where([
                    'order_sn'=>$seq
                ])->find();
                if($order&&$order['status']!=2){
                    $machine  = \app\common\model\vending\machine\Machine::get([
                        'device_no'=>$device_no
                    ]);
//                    \app\admin\model\vending\machine\Order::where([
//                        'order_sn'=>$seq
//                    ])->update([
//                        'status'=>2,
//                        'out_status'=>'1',
//                        'finishtime'=>time()
//                    ]);
                    $item = OrderGoods::where([
                        'order_id'=>$order['id']
                    ])->find();
                    $goods = \app\admin\model\vending\Goods::get($item['goods_id']);
//                    OrderGoods::where([
//                        'order_id'=>$order['id']
//                    ])->update([
//                        'finishtime'=>time(),
//                        'out_status'=>'1'
//                    ]);
                    $total = $order['pay_fee'];
                    if($goods['spec_num']>=$data['WEIGHT']){
                        $refund_money = round($item['pay_amount']-($item['pay_amount']*($data['WEIGHT']/$goods['spec_num'])),2);

                        \app\admin\model\vending\machine\Order::startRefund($order, $item, 0.1, [], '未出冰退款');
                    }
die;
                    $list = \app\common\model\vending\machine\profit\User::where([
                        'machine_id'=>$machine->id
                    ])->select();
                    foreach ($list as $k=>$v){
                        if($v['rate'] > 0){
                            $amount = round($total/100*$v['rate'],2);
                            $user = \app\admin\model\User::get($v['user_id']);
                            UserService::changeProfit($user, $amount, 1, '收益发放', $v['machine_id']);

                        }
                    }


                }
                break;
        }
    }
    /**
     * 订单状态更改，
     *
     */
    public function write_off()
    {

        $order_sn = $_REQUEST['order_sn'];
        $device_no = $_REQUEST['device_no'];

        $order = \app\admin\model\vending\machine\Order::where([
            'order_sn'=>$order_sn
        ])->find();

        if($order&&$order['status']!=2){
            $machine  = Machine::get([
                'device_no'=>$device_no
            ]);
            \app\admin\model\vending\machine\Order::where([
                'order_sn'=>$order_sn
            ])->update([
                'status'=>2,
                'out_status'=>'1',
                'finishtime'=>time()
            ]);
            OrderGoods::where([
                'order_id'=>$order['id']
            ])->update([
                'finishtime'=>time(),
                'out_status'=>'1'
            ]);
            $total = $order['pay_fee'];

            $list = \app\common\model\vending\machine\profit\User::where([
                'machine_id'=>$machine->id
            ])->select();
            foreach ($list as $k=>$v){
                if($v['rate'] > 0){
                    $amount = round($total/100*$v['rate'],2);
                    $user = \app\admin\model\User::get($v['user_id']);
                    UserService::changeProfit($user, $amount, 1, '收益发放', $v['machine_id']);

                }
            }


        }

        $this->success('请求成功');
    }
    public function callback(Request $request)
    {
        // 1.获取OSS的签名header和公钥url header
        $authorizationBase64 = "";
        $pubKeyUrlBase64 = "";
        /*
        * 注意：如果要使用HTTP_AUTHORIZATION头，你需要先在apache或者nginx中设置rewrite，以apache为例，修改
        * 配置文件/etc/httpd/conf/httpd.conf(以你的apache安装路径为准)，在DirectoryIndex index.php这行下面增加以下两行
            RewriteEngine On
            RewriteRule .* - [env=HTTP_AUTHORIZATION:%{HTTP:Authorization},last]
        * */
        if (isset($_SERVER['HTTP_AUTHORIZATION']))
        {
            $authorizationBase64 = $_SERVER['HTTP_AUTHORIZATION'];
        }
        if (isset($_SERVER['HTTP_X_OSS_PUB_KEY_URL']))
        {
            $pubKeyUrlBase64 = $_SERVER['HTTP_X_OSS_PUB_KEY_URL'];
        }
        if ($authorizationBase64 == '' || $pubKeyUrlBase64 == '')
        {
            header("http/1.1 403 Forbidden");
            exit();
        }

        // 2.获取OSS的签名
        $authorization = base64_decode($authorizationBase64);

        // 3.获取公钥
        $pubKeyUrl = base64_decode($pubKeyUrlBase64);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $pubKeyUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        $pubKey = curl_exec($ch);
        if ($pubKey == "")
        {
            //header("http/1.1 403 Forbidden");
            exit();
        }

        // 4.获取回调body
        $body = file_get_contents('php://input');

        // 5.拼接待签名字符串
        $authStr = '';
        $path = $_SERVER['REQUEST_URI'];
        $pos = strpos($path, '?');
        if ($pos === false)
        {
            $authStr = urldecode($path)."\n".$body;
        }
        else
        {
            $authStr = urldecode(substr($path, 0, $pos)).substr($path, $pos, strlen($path) - $pos)."\n".$body;
        }

        // 6.验证签名
        $ok = openssl_verify($authStr, $authorization, $pubKey, OPENSSL_ALGO_MD5);
        if ($ok == 1)
        {
            $param = $request->param();
            //根据返回的信息 $param 根据需要保存自数据库


        }
        else{
            exit();
        }
    }

    //故障通知
    public function send_gz_msg($machine,$user,$fault){
//1:碎冰机故障，2:绞龙电机故障，3:拉冰电机故障，4:侧推电机故障， 5:落冰超时故障，6:落冰限位开关故障，7:推冰限位开关故障
        $fault_arr = [
            '1'=>'碎冰机故障',
            '2'=>'绞龙电机故障',
            '3'=>'拉冰电机故障',
            '4'=>'侧推电机故障',
            '5'=>'落冰超时故障',
            '6'=>'落冰限位开关故障',
            '7'=>'推冰限位开关故障',
            '8'=>'制冰机故障',

        ];

        $oauth = UserOauth::where([
            'user_id'=>$user['id']
        ])->find();


        $data = array(
            "touser" =>  $oauth['mp_openid'],
            "template_id" => 'dO7C6DklX2i4cCdaHOlxVnSUcPSwdWluAZ8UvSt3jSc',
            "url" => '',
            "data" => array(
                'thing1' => array('value' =>$machine['name'], 'color' => '#173177'),
                'thing2' => array('value' => mb_substr($machine['address'],-12,12), 'color' => '#173177'),
                'time3' => array('value' => date('Y-m-d H:i:s'), 'color' => '#173177'),
                'const4' => array('value' => $fault_arr[$fault], 'color' => '#173177'),

            ),
        );
dump($data);
        $res = (new Push())->send($data);

      
        $res = \app\common\library\Sms::notice($user['mobile'],'您的售卖冰块小程序当前出现离线或故障，请及时查看设备状态并处理，以确保
服务正常运行。如有疑问可咨询客服','SMS_489700295');



    }
    //离线通知
    public function send_lx_msg(){



        $list = Machine::all();
        foreach ($list as $k=>$v){

            $log = \app\admin\model\vending\machine\Log::where([
                'device_no'=>$v['device_no'],
                'createtime'=>['>=',time()-10*61]
            ])->find();
            MachineStatus::where([
                'device_no'=>$v['device_no'],
            ])->update([
                'net_status'=>0
            ]);
            if(!$log){
                if($v['msg']==1){
continue;
                }
                $machine =$v;
                $user_id = Group::where([
                    'id'=>$v['group_id']
                ])->value('user_id');

                $user = \app\admin\model\User::get($user_id);


                $oauth = UserOauth::where([
                    'user_id'=>$user['id']
                ])->find();


                $data = array(
                    "touser" =>  $oauth['mp_openid'],
                    "template_id" => 'hFjVXRlEDq6j-WfOdLshaAhcCx9L4VPJ_NRGp5W01GQ',
                    "url" => '',
                    "data" => array(
                        'character_string1' => array('value' =>$machine['machine_no'], 'color' => '#173177'),
                        'thing2' => array('value' => $machine['address'], 'color' => '#173177'),
                        'time3' => array('value' => date('Y-m-d H:i:s'), 'color' => '#173177'),

                    ),
                );

                $res = (new Push())->send($data);

                Machine::where([
                    'id'=>$v['id']
                ])->update([
                    'msg'=>'1'
                ]);
//                $res = \app\common\library\Sms::notice($user['mobile'],'您的售卖冰块小程序当前出现离线或故障，请及时查看设备状态并处理，以确保
//服务正常运行。如有疑问可咨询客服','SMS_489700295');
            }else{

                Machine::where([
                    'id'=>$v['id']
                ])->update([
                    'msg'=>'0'
                ]);
            }

        }

    }
    //更新设备网络状态
    public function set_machine_status(){
        $time = time() - 10 * 60;
        $where['updatetime'] = ['lt',$time];
        $where['net_status'] = 1;
        $res = MachineStatus::where($where)->update(['net_status'=>0]);
        echo '成功';
    }

    //自动清理一个月前日志
    public function autoclearlog(){

        $last_month=date('Ym',strtotime('-1 month'));


        if(is_dir('../runtime/log/'.$last_month)){
            $loglist=scandir('../runtime/log/'.$last_month);

            foreach ($loglist as $k=>$v){
                if(strpos($v,'.log')){
                    unlink('../runtime/log/'.$last_month.'/'.$v);
                }
            }
        }
        if(is_dir('../runtime/log_cli/'.$last_month)) {
            $loglist = scandir('../runtime/log_cli/' . $last_month);

            foreach ($loglist as $k => $v) {
                if (strpos($v, '.log')) {
                    unlink('../runtime/log_cli/' . $last_month . '/' . $v);
                }
            }
        }
    }


    //
    public function get_message(){
        $config = config('site');
        $this->success('ok',$config);
    }


}
