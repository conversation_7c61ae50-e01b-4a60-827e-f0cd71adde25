# 售后申请退款功能实现

## 项目概述
为后台的售后申请增加退款功能，支持两种支付方式的退款：
- wallet余额支付：直接退回user表的money字段
- wechat微信支付：退回用户微信余额
- alipay支付宝支付：退回用户支付宝余额

## 实现方案
采用独立退款服务类的方案，兼容微信/支付宝/余额三种退款方式，支持多次退款。

## 核心文件修改

### 1. 创建退款服务类
**文件**: `application/common/service/RefundService.php`
**功能**:
- 统一的退款接口，根据支付类型自动选择退款方式
- 支持多次退款，维护refunded_amount字段
- 退款金额验证：确保不超过实付金额
- 完整的异常处理和日志记录

**关键方法**:
- `processAfterSalesRefund()`: 处理售后申请退款的主方法
- `validateRefundAmount()`: 验证退款金额
- `executeRefund()`: 根据支付方式执行退款
- `processWalletRefund()`: 处理余额退款
- `processWechatRefund()`: 处理微信退款
- `processAlipayRefund()`: 处理支付宝退款

### 2. 更新售后申请控制器
**文件**: `application/admin/controller/OrderAfterSales.php`
**新增方法**: `refund($id)`
**功能**:
- 验证售后申请状态（必须是审核通过状态）
- 调用RefundService处理退款
- 更新售后申请状态为"已退款"
- 返回JSON响应给前端

### 3. 更新售后申请模型
**文件**: `application/common/model/shop/OrderAfterSales.php`
**新增方法**:
- `canRefund()`: 验证是否可以退款
- `isRefunded()`: 验证是否已经退款
- `getRefundAmount()`: 获取退款金额
- `getOrderRemainRefundAmount()`: 获取订单剩余可退款金额
- `validateRefundAmount()`: 验证退款金额是否有效

### 4. 前端交互实现
**文件**: `public/assets/js/backend/order_after_sales.js`
**功能**:
- 在操作列添加"退款"按钮（仅对审核通过的申请显示）
- 实现退款确认对话框
- AJAX请求处理退款操作
- 成功后刷新表格数据

## 多次退款支持

### 退款金额验证逻辑
```php
// 计算剩余可退款金额
$remainingAmount = $order->pay_fee - $order->refunded_amount;

// 验证退款金额
if ($refundAmount > $remainingAmount) {
    throw new Exception("退款金额不能超过剩余可退款金额：{$remainingAmount}元");
}
```

### refunded_amount字段维护
每次退款成功后，自动累加退款金额到订单的refunded_amount字段，并更新售后状态：
```php
$newRefundedAmount = $order->refunded_amount + $refundAmount;
$order->refunded_amount = $newRefundedAmount;

// 更新售后状态为售后完成
$order->after_sales_status = 2; // 2=售后完成

$order->save();
```

## 退款回调处理
- 使用与支付相同的回调路径：`/api/machine/pay/notify/payment/{payType}/platform/{platform}`
- 复用现有的支付回调处理机制
- 确保退款成功后正确更新相关状态

## 使用说明

### 管理员操作流程
1. 进入售后申请管理页面
2. 找到状态为"审核通过"的申请
3. 点击"退款"按钮
4. 确认退款操作
5. 系统自动根据原支付方式执行退款
6. 退款成功后，售后申请状态更新为"已退款"，订单的售后状态更新为"售后完成"

### 支持的退款方式
- **余额支付**: 直接操作用户余额，并创建完整的余额变动记录
- **微信支付**: 调用微信退款API，退款到用户微信零钱
- **支付宝支付**: 调用支付宝退款API，退款到用户支付宝账户

### 余额变动记录
根据实际的 `fa_user_money_log` 表结构，余额退款时会创建包含以下字段的记录：
- `user_id`: 用户ID
- `money`: 变更金额（退款金额）
- `before`: 变更前余额
- `after`: 变更后余额
- `type`: '1' (增加)
- `order_type`: '2' (售货机)
- `item_id`: 订单ID
- `memo`: 退款备注
- `createtime`: 创建时间

### 退款金额限制
- 单次退款金额不能超过剩余可退款金额
- 剩余可退款金额 = 订单实付金额 - 已退款总金额
- 支持对同一订单进行多次部分退款

## 技术要点

### 事务处理
所有退款操作都在数据库事务中执行，确保数据一致性。

### 日志记录
详细记录退款过程的关键步骤和结果，便于问题排查。

### 异常处理
完善的异常处理机制，确保退款失败时能正确回滚和提示。

### 回调地址配置
退款回调地址与支付回调使用相同的路径格式，确保兼容性。

## 测试建议

### 功能测试
1. 测试余额支付订单的退款
2. 测试微信支付订单的退款
3. 测试支付宝支付订单的退款
4. 测试多次部分退款
5. 测试退款金额超限的验证
6. 测试非审核通过状态的退款限制

### 异常测试
1. 测试网络异常时的退款处理
2. 测试支付接口返回错误时的处理
3. 测试数据库异常时的事务回滚

## 数据库字段要求

### fa_user_money_log 表字段
确保用户余额变动记录表包含以下字段：
- `type` enum('1','2'): 变动类型，1=增加，2=减少
- `order_type` enum('1','2'): 订单类型，1=商城，2=售货机
- `item_id` int(10): 项目ID（订单ID）
- `memo` varchar(255): 备注信息

### fa_vending_machine_order 表字段
确保售货机订单表包含：
- `refunded_amount` decimal(10,2): 已退款总金额字段
- `after_sales_status` tinyint(1): 售后状态字段（0=未售后，1=售后中，2=售后完成）

## 注意事项
1. 退款功能仅对审核通过的售后申请开放
2. 退款使用商户订单号，确保与原支付订单匹配
3. 退款回调处理复用现有机制，无需额外开发
4. 所有退款操作都有完整的日志记录
5. 支持多次退款，但总退款金额不能超过实付金额
6. 余额退款直接操作数据库，确保记录字段完整性
