<?php

namespace app\api\controller\machine;

use app\common\model\Ad;
use app\common\model\vending\Config;
use app\common\model\vending\goods\Category;
use app\common\model\vending\goods\Goods;
use app\common\model\vending\machine\MachineStatus;
use app\common\model\vending\machine\Type;
use app\common\model\vending\machine\Way;
use app\common\service\machine\WayService;
use think\exception\ValidateException;

/**
 * 售货机首页接口
 */
class Index extends Base
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    /**
     * 售货机首页
     */
    public function index()
    {
        // 获取用户经纬度参数
        $user_lat = $this->request->param('lat');
        $user_lng = $this->request->param('lng');

        // 参数验证和类型转换
        $user_lat = (!empty($user_lat) && is_numeric($user_lat)) ? (float)$user_lat : 39.9042; // 默认北京纬度
        $user_lng = (!empty($user_lng) && is_numeric($user_lng)) ? (float)$user_lng : 116.4074; // 默认北京经度

        $machine = $this->machine;

        // 计算用户与机器的距离
        if ($machine->lat && $machine->lon && is_numeric($machine->lat) && is_numeric($machine->lon)) {
            $machine_lat = (float)$machine->lat;
            $machine_lng = (float)$machine->lon;
            $machine->distance = $this->calculateDistance($user_lat, $user_lng, $machine_lat, $machine_lng);
        } else {
            $machine->distance = 0; // 如果机器没有经纬度信息，距离设为0
        }
        $machine->max_buy_num = Type::where('id',$machine['type_id'])->value('max_buy');
//        $category = new Category();
//        $category_list = $category->where('agent_id', $this->machine->agent_id)->where('status', 1)->field('id,name,image,active_image')->order('weigh asc')->select();
//        $category_list = collection($category_list)->toArray();
//        $way = new Way();
//        foreach ($category_list as $key => $cate) {
////            $category_list[$key]['ways'] = $way->hidden(['goods'])->field('no,goods_id,machine_price,custom_price')->with(['goods' => function ($query) use ($cate) {
//////                $query->where('category_id', $cate['id'])->withField('name,image');
//////            }])->where(['machine_id' => $this->machineID])->select();
//            $category_list[$key]['ways'] = $way->field('id,no')->with(['goods' => function ($query) use ($cate) {
//                $query->where('category_id', $cate['id'])->withField('id,name,image,price,market_price');
//            }])->where(['machine_id' => $this->machine->id])->select();
//        }
//        //机器广告
//        $ad = Ad::where('machine_id',$this->machine->id)->where('type', 1)->find();
        $ad = Ad::field('id,mime,image')->where('machine_id','in', $this->machine->id)
//            ->where('start_time', '<= time', time())
//            ->where('end_time', '>= time', time())
            ->select();
        $notice = Ad::field('title,content')->where('machine_id','in', $this->machine->id)
//            ->where('start_time', '<= time', time())
//            ->where('end_time', '>= time', time())
            ->find();

        $way = \app\admin\model\vending\Goods::where([
            'id'=>['in',$machine->goods_ids],
            'type'=>'1',
            'deletetime'=>null
        ])->order('weigh desc')->select();


        foreach ($way as $k=>$v){

            $way[$k]['price'] =  round($v['price']*$machine['machine_discount']/100,2);

        }

        $data = Config::where(['name'=>'chat'])->value('value');
        $data = json_decode($data,true);
        $tel = $data['kefu_mobile'];
//        foreach ($ad as $k=>$v){
//            $ad[$k]['image'] = 'https://'.$_SERVER['HTTP_HOST'].$v['image'];
//        }

        $machine_status = MachineStatus::where([
            'device_no'=>$machine['device_no']
        ])->find();

        if(!$machine_status||($machine_status['status'] != 0&&$machine_status['net_status'] != 1) ){
            $this->error('机器状态异常');
        }

        $this->success('', compact('machine','ad','way','notice','tel'));
//        $this->success('', compact('machine'));
    }

    /**
     * 首页广告
     */
    public function ad()
    {
        $ad = Ad::field('id,mime,image,start_time,end_time')->where('machine_id', $this->machine->id)->where('type', 1)
            ->where('start_time', '<= time', time())
            ->where('end_time', '>= time', time())
            ->order('id desc')
            ->find();
        $this->success('', $ad);
    }
    /**
     * 售货机广告
     */
    public function machine_ad()
    {
        $ad = Ad::field('id,image')->where('machine_id', $this->machine->id)
            ->order('id desc')
            ->find();
        $this->success('', $ad);
    }

//    public function goods()
//    {
//        $category_id = $this->request->param('id');
//        $category = new Category();
//        $category_query = $category->where('agent_id', $this->machine->agent_id)->where('status', 1);
//        if($category_id){
//            $category_query->where('id',$category_id);
//        }
//        $category_list = $category_query->field('id,name,image,active_image')->order('weigh asc')->select();
//        $category_list = collection($category_list)->toArray();
//        $new_category_list = [];
//        foreach ($category_list as $key => $cate) {
//            //  $goods = Goods::alias('goods')->where('goods.category_id',$cate['id'])->join('vending_machine_way way','goods.id = way.goods_id','right')->where('way.machine_id',$this->machine->id)->where('way.goods_id','>',0)->where('way.is_active',1)->field('goods.id,goods.name,goods.image,goods.price,goods.market_price,way.id as way_id,way.no,way.machine_id,way.stock')->select();
//            $goods = Goods::where('goods.type',1)->where('category_id',$cate['id'])->column('id');
//            $
////                ->where('goods.status',2)
////                ->where('goods.category_id',$cate['id'])->join('vending_machine_way way','goods.id = way.goods_id','right')->where('way.machine_id',$this->machine->id)->where('way.goods_id','>',0)->where('way.is_active',1)->field('goods.id,goods.name,goods.image,goods.price,goods.market_price,way.id as way_id,way.no,way.machine_id,way.stock')->select();
//////            $goods = Goods::alias('goods')->where('goods.category_id',$cate['id'])->join('vending_machine_way way','goods.id = way.goods_id','right')->where('way.machine_id',$this->machine->id)->where('way.goods_id','>',0)->where('way.is_active',1)->field('goods.id,goods.name,goods.image,goods.market_price,way.id as way_id,way.no,way.machine_id,way.stock,way.custom_price as price')->select();
//////            if($goods){
////                $new_category_list[$key]['goods'] = $goods;
////            }
//            if($goods){
//                $category_list[$key]['goods']=$goods;
//            }else{
//                unset($category_list[$key]);
//            }
//
//        }
//        $this->success('', $category_list);
//
//    }
    public function goods()
    {
        $category_id = $this->request->param('id');
        $category = new Category();
//        ->where('agent_id', $this->machine->agent_id)
        $category_query = $category->where('status', 1);
        if($category_id){
            $category_query->where('id',$category_id);
        }
        $category_list = $category_query->field('id,name,image,active_image')->order('weigh asc')->select();
        $category_list = collection($category_list)->toArray();
        $new_category_list = [];
        foreach ($category_list as $key => $cate) {
          //  $goods = Goods::alias('goods')->where('goods.category_id',$cate['id'])->join('vending_machine_way way','goods.id = way.goods_id','right')->where('way.machine_id',$this->machine->id)->where('way.goods_id','>',0)->where('way.is_active',1)->field('goods.id,goods.name,goods.image,goods.price,goods.market_price,way.id as way_id,way.no,way.machine_id,way.stock')->select();
            $goods = Goods::alias('goods')
                ->where('goods.type',1)
//                ->where('goods.status',2)
//                ->where('goods.category_id',$cate['id'])->join('vending_machine_way way','goods.id = way.goods_id','right')->where('way.machine_id',$this->machine->id)->where('way.goods_id','>',0)->where('way.is_active',1)->field('goods.id,goods.name,goods.image,goods.price,goods.market_price,way.id as way_id,way.no,way.machine_id,way.stock')->select();
                ->where('goods.category_id',$cate['id'])->join('vending_machine_way way','goods.id = way.goods_id','right')->where('way.machine_id',$this->machine->id)->where('way.goods_id','>',0)->where('way.is_active',1)->field('goods.id,goods.name,goods.image,way.custom_price as price,goods.market_price,way.id as way_id,way.no,way.machine_id,way.stock')->select();
//            $goods = Goods::alias('goods')->where('goods.category_id',$cate['id'])->join('vending_machine_way way','goods.id = way.goods_id','right')->where('way.machine_id',$this->machine->id)->where('way.goods_id','>',0)->where('way.is_active',1)->field('goods.id,goods.name,goods.image,goods.market_price,way.id as way_id,way.no,way.machine_id,way.stock,way.custom_price as price')->select();
//            if($goods){
//                $new_category_list[$key]['goods'] = $goods;
//            }
            if($goods){
                $category_list[$key]['goods']=$goods;
            }else{
                unset($category_list[$key]);
            }

        }
        $this->success('', $category_list);

    }

    public function goods2()
    {
        $category_id = $this->request->param('id');

        $category = new Category();
        $category_query = $category->where('agent_id', $this->machine->agent_id)->where('status', 1);
        if($category_id){
            $category_query->where('id',$category_id);
        }
        $category_list = $category_query->field('id,name,image,active_image')->order('weigh asc')->select();
        $category_list = collection($category_list)->toArray();
        foreach ($category_list as $key => $cate) {
            $ways = Way::field('id,no')->with(['goods' => function ($query) use ($cate) {
                $query->where('category_id', $cate['id'])->withField('id,name,image,price,market_price');
            }])->where(['machine_id' => $this->machine->id])->select();
            foreach ($ways as $way){
                $way->goods_id = $way->goods->id;
                $way->goods_name = $way->goods->name;
                $way->goods_image = $way->goods->image;
                $way->goods_price = $way->goods->price;
                $way->goods_market_price = $way->goods->market_price;
                unset($way->goods);
            }

            $category_list[$key]['ways'] = $ways;
        }
        $this->success('', $category_list);

    }


    public function category(){

        $machine_goods_ids = Way::where('machine_id',$this->machine->id)->column('goods_id');
        $show_category_ids = Goods::where('id','in',$machine_goods_ids)->column('category_id');
//        where('agent_id', $this->machine->agent_id)
//                                ->
        $category_list = Category::where('id','in',$show_category_ids)
                                ->where('status', 1)->field('id,name,image,active_image')->order('weigh asc')->select();
        $this->success('', $category_list);
    }

    //这个接口只获取通道设置了的产品,暂未启用
    public function index2()
    {
        $machine = $this->machine;
        $way = new Way();
        $goods = $way->where(['machine_id' => $this->machine->id])->whereNotNull('goods_id')->select();
        $list = [];
        foreach ($goods as $row) {
            $row->goods_name = $row->goods->name;
            $row->goods_image = $row->goods->image;
            $row->category_id = $row->goods->category_id;
            // $row->category_name = $row->goods->category->name;
            //$list[$row['category_id']]['category_name'] = $row->goods->category->name;
            $list[$row['category_id']]['category_name'] = $row->goods->category->name;
            $list[$row['category_id']]['category_id'] = $row->category_id;
            unset($row['goods']);
            //$row->visible([''])
            $list[$row['category_id']]['goods'][] = $row;
        }
        //广告
        $ad = Ad::where('machine_id', $this->machine->id)->where('type', 1)->find();
        $this->success('', compact('machine', 'list', 'ad'));
    }

    /**
     * 计算两点间距离（公里）
     * 使用Haversine公式
     * @param float $lat1 用户纬度
     * @param float $lng1 用户经度
     * @param float $lat2 机器纬度
     * @param float $lng2 机器经度
     * @return float 距离（公里）
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earth_radius = 6371; // 地球半径（公里）

        $lat1_rad = deg2rad($lat1);
        $lng1_rad = deg2rad($lng1);
        $lat2_rad = deg2rad($lat2);
        $lng2_rad = deg2rad($lng2);

        $delta_lat = $lat2_rad - $lat1_rad;
        $delta_lng = $lng2_rad - $lng1_rad;

        $a = sin($delta_lat / 2) * sin($delta_lat / 2) +
             cos($lat1_rad) * cos($lat2_rad) *
             sin($delta_lng / 2) * sin($delta_lng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        $distance = $earth_radius * $c;

        return round($distance, 2); // 保留2位小数
    }


}
