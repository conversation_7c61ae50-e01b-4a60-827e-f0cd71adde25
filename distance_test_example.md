# 距离计算功能测试说明

## 功能实现

已在 `application/api/controller/machine/Index.php` 的 `index` 方法中实现了用户与机器之间的距离计算功能。

## 实现细节

### 1. 参数接收
- `lat`: 用户纬度（可选，默认值：39.9042 - 北京纬度）
- `lng`: 用户经度（可选，默认值：116.4074 - 北京经度）
- **参数验证**：自动处理空值、非数字值，确保类型安全

### 2. 距离计算
- 使用 Haversine 公式计算两点间的球面距离
- 距离单位：公里
- 精度：保留2位小数

### 3. 返回数据
在原有返回数据的基础上，machine对象中新增了 `distance` 字段，表示用户与当前机器的距离。

## API调用示例

### 请求方式
```
GET/POST /api/machine/index
```

### 请求参数
```
machine_no: 机器编号（必需，通过header或参数传递）
lat: 用户纬度（可选）
lng: 用户经度（可选）
```

### 示例请求
```
# 使用默认经纬度（北京）
GET /api/machine/index?machine_no=MACHINE001

# 使用自定义经纬度（上海：31.2304, 121.4737）
GET /api/machine/index?machine_no=MACHINE001&lat=31.2304&lng=121.4737
```

### 返回数据示例
```json
{
    "code": 1,
    "msg": "",
    "time": "1640995200",
    "data": {
        "machine": {
            "id": 1,
            "name": "测试机器",
            "machine_no": "MACHINE001",
            "lat": "39.9000",
            "lon": "116.4000",
            "distance": 0.52,  // 新增：距离字段（公里）
            "max_buy_num": 10,
            // ... 其他机器信息
        },
        "ad": [...],
        "way": [...],
        "notice": {...},
        "tel": "..."
    }
}
```

## 测试场景

### 1. 默认测试
- 不传递lat和lng参数
- 系统使用北京经纬度（39.9042, 116.4074）作为用户位置
- 计算与机器的距离

### 2. 自定义位置测试
- 传递具体的lat和lng参数
- 计算用户实际位置与机器的距离

### 3. 边界情况处理
- 机器没有经纬度信息时，distance返回0
- 参数为空字符串时，使用默认值
- 参数为非数字时，使用默认值
- 参数类型自动转换为float，避免deg2rad()报错

## 距离计算公式

使用 Haversine 公式：
```
a = sin²(Δφ/2) + cos φ1 ⋅ cos φ2 ⋅ sin²(Δλ/2)
c = 2 ⋅ atan2( √a, √(1−a) )
d = R ⋅ c
```

其中：
- φ 是纬度
- λ 是经度
- R 是地球半径（6371公里）
- d 是两点间的距离

## 注意事项

1. 距离计算基于球面几何，适用于大多数应用场景
2. 默认使用北京经纬度便于测试
3. 机器必须有有效的经纬度信息才能计算距离
4. 距离单位为公里，保留2位小数精度
