<?php

namespace app\common\service;

use app\api\library\Pay;
use app\common\model\vending\machine\Order as VendingOrder;
use app\common\model\shop\OrderAfterSales;
use think\Db;
use think\Exception;
use think\Log;

/**
 * 退款服务类
 * 支持微信、支付宝、余额三种退款方式
 * 支持多次退款，维护refunded_amount字段
 */
class RefundService
{
    /**
     * 处理售后申请退款
     * @param OrderAfterSales $afterSales 售后申请对象
     * @param float $refundAmount 退款金额
     * @param string $remark 退款备注
     * @return bool
     * @throws Exception
     */
    public function processAfterSalesRefund($afterSales, $refundAmount, $remark = '售后退款')
    {
        // 获取关联的订单
        $order = VendingOrder::where('id', $afterSales->order_id)->find();
        if (!$order) {
            throw new Exception('关联订单不存在');
        }

        // 验证退款金额
        $this->validateRefundAmount($order, $refundAmount);

        // 开始事务
        Db::startTrans();
        try {
            // 执行退款
            $result = $this->executeRefund($order, $refundAmount, $remark);
            
            if ($result) {
                // 更新订单已退款金额
                $this->updateOrderRefundedAmount($order, $refundAmount);
                
                // 更新售后申请状态
                $this->updateAfterSalesStatus($afterSales, $refundAmount);
                
                Db::commit();
                return true;
            } else {
                Db::rollback();
                throw new Exception('退款处理失败');
            }
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 验证退款金额
     * @param VendingOrder $order
     * @param float $refundAmount
     * @throws Exception
     */
    private function validateRefundAmount($order, $refundAmount)
    {
        if ($refundAmount <= 0) {
            throw new Exception('退款金额必须大于0');
        }

        // 计算剩余可退款金额
        $remainingAmount = $order->pay_fee - $order->refunded_amount;
        
        if ($refundAmount > $remainingAmount) {
            throw new Exception("退款金额不能超过剩余可退款金额：{$remainingAmount}元");
        }
    }

    /**
     * 执行退款操作
     * @param VendingOrder $order
     * @param float $refundAmount
     * @param string $remark
     * @return bool
     * @throws Exception
     */
    private function executeRefund($order, $refundAmount, $remark)
    {
        switch ($order->pay_type) {
            case 'wallet':
                return $this->processWalletRefund($order, $refundAmount, $remark);
            case 'wechat':
                return $this->processWechatRefund($order, $refundAmount, $remark);
            case 'alipay':
                return $this->processAlipayRefund($order, $refundAmount, $remark);
            default:
                throw new Exception('不支持的支付方式：' . $order->pay_type);
        }
    }

    /**
     * 处理余额退款
     * @param VendingOrder $order
     * @param float $refundAmount
     * @param string $remark
     * @return bool
     */
    private function processWalletRefund($order, $refundAmount, $remark)
    {
        try {
            // 直接操作用户余额和记录，确保字段完整
            $user = \app\common\model\User::where('id', $order->user_id)->find();
            if (!$user) {
                throw new Exception('用户不存在');
            }

            // 计算余额变动
            $before = $user->money;
            $after = $before + $refundAmount;

            // 更新用户余额
            $user->money = $after;
            $user->save();

            // 创建余额变动记录，包含所有必要字段
            \app\common\model\MoneyLog::create([
                'user_id' => $user->id,
                'money' => $refundAmount,
                'before' => $before,
                'after' => $after,
                'type' => '1', // 1=增加
                'order_type' => '2', // 2=售货机
                'item_id' => $order->id,
                'memo' => $remark,
                'createtime' => time()
            ]);

            Log::write("余额退款成功：订单{$order->order_sn}，金额{$refundAmount}元");
            return true;
        } catch (Exception $e) {
            Log::write("余额退款失败：订单{$order->order_sn}，错误：" . $e->getMessage());
            throw new Exception('余额退款失败：' . $e->getMessage());
        }
    }

    /**
     * 处理微信退款
     * @param VendingOrder $order
     * @param float $refundAmount
     * @param string $remark
     * @return bool
     * @throws Exception
     */
    private function processWechatRefund($order, $refundAmount, $remark)
    {
        $orderData = [
            'out_trade_no' => $order->order_sn,
            'out_refund_no' => time() . mt_rand(1000, 9999),
            'total_fee' => $order->pay_fee * 100, // 微信金额单位为分
            'refund_fee' => $refundAmount * 100,
            'refund_desc' => $remark,
        ];

        return $this->callPaymentRefund($order, $orderData, 'wechat');
    }

    /**
     * 处理支付宝退款
     * @param VendingOrder $order
     * @param float $refundAmount
     * @param string $remark
     * @return bool
     * @throws Exception
     */
    private function processAlipayRefund($order, $refundAmount, $remark)
    {
        $orderData = [
            'out_trade_no' => $order->order_sn,
            'out_request_no' => time() . mt_rand(1000, 9999),
            'refund_amount' => $refundAmount,
            'refund_reason' => $remark,
        ];

        return $this->callPaymentRefund($order, $orderData, 'alipay');
    }

    /**
     * 调用支付接口退款
     * @param VendingOrder $order
     * @param array $orderData
     * @param string $payType
     * @return bool
     * @throws Exception
     */
    private function callPaymentRefund($order, $orderData, $payType)
    {
        try {
            // 构建回调地址 - 使用与支付相同的回调路径
            $notifyUrl = request()->root(true) . '/api/machine/pay/notify/payment/' . $payType . '/platform/' . $order->platform;

            // 创建支付对象
            $pay = new Pay($payType, $order->platform, $notifyUrl, $order->agent_id);

            // 执行退款
            $result = $pay->refund($orderData);
            
            Log::write("退款请求结果：" . json_encode($result));

            // 处理退款结果
            if ($payType === 'wechat') {
                if ($result['return_code'] === 'SUCCESS' && $result['result_code'] === 'SUCCESS') {
                    Log::write("微信退款成功：订单{$order->order_sn}");
                    return true;
                } else {
                    $errorMsg = $result['return_msg'] ?? $result['err_code_des'] ?? '未知错误';
                    throw new Exception("微信退款失败：{$errorMsg}");
                }
            } elseif ($payType === 'alipay') {
                if ($result['code'] === '10000') {
                    Log::write("支付宝退款成功：订单{$order->order_sn}");
                    return true;
                } else {
                    $errorMsg = $result['msg'] ?? $result['sub_msg'] ?? '未知错误';
                    throw new Exception("支付宝退款失败：{$errorMsg}");
                }
            }
            
            return false;
        } catch (Exception $e) {
            Log::write("退款接口调用失败：订单{$order->order_sn}，错误：" . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 更新订单已退款金额
     * @param VendingOrder $order
     * @param float $refundAmount
     */
    private function updateOrderRefundedAmount($order, $refundAmount)
    {
        $newRefundedAmount = $order->refunded_amount + $refundAmount;
        $order->refunded_amount = $newRefundedAmount;
        $order->save();
        
        Log::write("更新订单退款金额：订单{$order->order_sn}，新退款总额{$newRefundedAmount}元");
    }

    /**
     * 更新售后申请状态
     * @param OrderAfterSales $afterSales
     * @param float $refundAmount
     */
    private function updateAfterSalesStatus($afterSales, $refundAmount)
    {
        $afterSales->status = OrderAfterSales::STATUS_REFUNDED; // 已退款
        $afterSales->approved_amount = $refundAmount;
        $afterSales->refund_time = time();
        $afterSales->save();
        
        Log::write("更新售后申请状态：申请单{$afterSales->application_sn}，状态已退款");
    }

    /**
     * 获取订单剩余可退款金额
     * @param int $orderId
     * @return float
     */
    public function getRemainRefundAmount($orderId)
    {
        $order = VendingOrder::where('id', $orderId)->find();
        if (!$order) {
            return 0;
        }
        
        return $order->pay_fee - $order->refunded_amount;
    }
}
