<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\service\RefundService;
use app\common\model\shop\OrderAfterSales as CommonOrderAfterSales;
use think\Exception;

/**
 * 订单售后申请管理
 *
 * @icon fa fa-circle-o
 */
class OrderAfterSales extends Backend
{

    /**
     * OrderAfterSales模型对象
     * @var \app\admin\model\OrderAfterSales
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\OrderAfterSales;
        $this->view->assign("typeList", $this->model->getTypeList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    ->with(['user','agent','order'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {

                $row->getRelation('user')->visible(['nickname','mobile']);
				$row->getRelation('agent')->visible(['name']);
				$row->getRelation('order')->visible(['device_no','goods_amount','total_amount','pay_fee','delivery_consignee','delivery_mobile','delivery_address','dispatch_fee']);
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 处理退款
     */
    public function refund($id = null)
    {
        if (!$this->request->isAjax()) {
            $this->error('非法请求');
        }

        if (empty($id)) {
            $this->error('参数错误');
        }

        try {
            // 获取售后申请记录
            $afterSales = CommonOrderAfterSales::where('id', $id)->find();
            if (!$afterSales) {
                $this->error('售后申请不存在');
            }

            // 验证状态
            if ($afterSales->status != CommonOrderAfterSales::STATUS_APPROVED) {
                $this->error('只有审核通过的申请才能退款');
            }

            // 检查是否已经退款
            if ($afterSales->status == CommonOrderAfterSales::STATUS_REFUNDED) {
                $this->error('该申请已经退款');
            }

            // 获取退款金额（使用审核通过的金额）
            $refundAmount = $afterSales->approved_amount ?: $afterSales->requested_amount;

            if ($refundAmount <= 0) {
                $this->error('退款金额必须大于0');
            }

            // 创建退款服务实例
            $refundService = new RefundService();

            // 处理退款
            $result = $refundService->processAfterSalesRefund(
                $afterSales,
                $refundAmount,
                '管理员处理售后退款'
            );

            if ($result) {
                $this->success('退款处理成功');
            } else {
                $this->error('退款处理失败');
            }

        } catch (Exception $e) {
            $this->error('退款失败：' . $e->getMessage());
        }
    }

}
