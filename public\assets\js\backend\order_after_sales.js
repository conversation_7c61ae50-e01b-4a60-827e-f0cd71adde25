define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'order_after_sales/index' + location.search,
                    add_url: 'order_after_sales/add',
                    edit_url: 'order_after_sales/edit',
                    del_url: 'order_after_sales/del',
                    multi_url: 'order_after_sales/multi',
                    import_url: 'order_after_sales/import',
                    table: 'order_after_sales',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'agent_id', title: __('Agent_id')},
                        {field: 'user_id', title: __('User_id')},
                        {field: 'order_id', title: __('Order_id')},
                        {field: 'order_sn', title: __('Order_sn'), operate: 'LIKE'},
                        {field: 'application_sn', title: __('Application_sn'), operate: 'LIKE'},
                        {field: 'type', title: __('Type'), searchList: {"1":__('Type 1'),"2":__('Type 2')}, formatter: Table.api.formatter.normal},
                        {field: 'reason', title: __('Reason'), operate: 'LIKE'},
                        {field: 'images', title: __('Images'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.images},
                        {field: 'requested_amount', title: __('Requested_amount'), operate:'BETWEEN'},
                        {field: 'approved_amount', title: __('Approved_amount'), operate:'BETWEEN'},
                        {field: 'status', title: __('Status'), searchList: {"0":__('Status 0'),"1":__('Status 1'),"2":__('Status 2'),"3":__('Status 3'),"4":__('Status 4')}, formatter: Table.api.formatter.status},
                        {field: 'admin_id', title: __('Admin_id')},
                        {field: 'admin_memo', title: __('Admin_memo'), operate: 'LIKE'},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'refund_time', title: __('Refund_time'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'user.nickname', title: __('User.nickname'), operate: 'LIKE'},
                        {field: 'user.mobile', title: __('User.mobile'), operate: 'LIKE'},
                        {field: 'agent.name', title: __('Agent.name'), operate: 'LIKE'},
                        {field: 'order.device_no', title: __('Order.device_no'), operate: 'LIKE'},
                        {field: 'order.goods_amount', title: __('Order.goods_amount'), operate:'BETWEEN'},
                        {field: 'order.total_amount', title: __('Order.total_amount'), operate:'BETWEEN'},
                        {field: 'order.pay_fee', title: __('Order.pay_fee'), operate:'BETWEEN'},
                        {field: 'order.delivery_consignee', title: __('Order.delivery_consignee'), operate: 'LIKE'},
                        {field: 'order.delivery_mobile', title: __('Order.delivery_mobile'), operate: 'LIKE'},
                        {field: 'order.delivery_address', title: __('Order.delivery_address'), operate: 'LIKE'},
                        {field: 'order.dispatch_fee', title: __('Order.dispatch_fee'), operate:'BETWEEN'},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            formatter: function (value, row, index) {
                                var html = Table.api.formatter.operate.call(this, value, row, index);

                                // 如果状态是审核通过(1)，添加退款按钮
                                if (row.status == '1') {
                                    html += '<a href="javascript:;" class="btn btn-warning btn-xs btn-refund" data-id="' + row.id + '" title="退款"><i class="fa fa-money"></i> 退款</a> ';
                                }

                                return html;
                            }
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
